package com.wealthfront.branchy;

import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
@Entity(discriminatorName = "type", subclasses = {
    TestApplicationData.class,
    TestBankruptcyData.class,
    TestBorrowerData.class,
    TestBorrowerWorkflowData.class,
    TestCreditPullData.class,
    TestCreditScoreData.class,
    TestLoanAmountData.class,
})
public abstract class TestMortgageData {

  @Value(
      optional = true,
      nullable = true
  )
  public abstract String getInternalId();

  public abstract void setInternalId(String id);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract Set<String> getFieldsToNullSet();

  public abstract void setFieldsToNullSet(Set<String> fieldsToNullSet);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestEntityAction getAction();

  public abstract void setAction(TestEntityAction action);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestDataType getDataType();

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestVestaOperationType getVestaOperationType();

}
