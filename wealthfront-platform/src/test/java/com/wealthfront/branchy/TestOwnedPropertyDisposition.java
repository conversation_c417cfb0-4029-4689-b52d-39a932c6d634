package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestOwnedPropertyDisposition {
  SOLD {
    @Override
    public <T> T visit(OwnedPropertyDispositionVisitor<T> visitor) {
      return visitor.visitSold();
    }
  },
  PENDING_SALE {
    @Override
    public <T> T visit(OwnedPropertyDispositionVisitor<T> visitor) {
      return visitor.visitPendingSale();
    }
  },
  RETAIN {
    @Override
    public <T> T visit(OwnedPropertyDispositionVisitor<T> visitor) {
      return visitor.visitRetain();
    }
  },;

  public abstract <T> T visit(OwnedPropertyDispositionVisitor<T> visitor);

  public interface OwnedPropertyDispositionVisitor<T> {

    T visitRetain();

    T visitPendingSale();

    T visitSold();

  }
}
