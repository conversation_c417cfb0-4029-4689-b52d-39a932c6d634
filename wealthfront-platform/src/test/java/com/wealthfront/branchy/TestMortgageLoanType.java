package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
public enum TestMortgageLoanType {
  CONVENTIONAL("Conventional") {
    @Override
    public <T> T visit(MortgageLoanTypeVisitor<T> visitor) {
      return visitor.caseConventional();
    }
  },

  FHA("FHA") {
    @Override
    public <T> T visit(MortgageLoanTypeVisitor<T> visitor) {
      return visitor.caseFha();
    }
  },

  USDA("USDA") {
    @Override
    public <T> T visit(MortgageLoanTypeVisitor<T> visitor) {
      return visitor.caseUsda();
    }
  },

  VA("VA") {
    @Override
    public <T> T visit(MortgageLoanTypeVisitor<T> visitor) {
      return visitor.caseVa();
    }
  };

  private final String value;

  TestMortgageLoanType(String value) {
    this.value = value;
  }

  public String getValue() {
    return this.value;
  }

  public abstract <T> T visit(MortgageLoanTypeVisitor<T> visitor);

  public interface MortgageLoanTypeVisitor<T> {

    T caseConventional();

    T caseFha();

    T caseUsda();

    T caseVa();

  }

}
