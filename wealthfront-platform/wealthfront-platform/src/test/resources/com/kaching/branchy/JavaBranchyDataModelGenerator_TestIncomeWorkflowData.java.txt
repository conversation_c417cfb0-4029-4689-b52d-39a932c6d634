package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity
public class TestIncomeWorkflowData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasCapitalGains;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasDividendsAndInterest;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasSocialSecurity;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasPension;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasRetirementAccountDistributions;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasDisability;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasAlimony;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasSeparateMaintenance;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasChildSupport;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasOtherIncome;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean hasReviewedEmployment;

  public TestIncomeWorkflowData() {
    // JSON
  }

  public TestIncomeWorkflowData(TestEntityAction action, String id, Boolean hasCapitalGains,
      Boolean hasDividendsAndInterest, Boolean hasSocialSecurity, Boolean hasPension,
      Boolean hasRetirementAccountDistributions, Boolean hasDisability, Boolean hasAlimony,
      Boolean hasSeparateMaintenance, Boolean hasChildSupport, Boolean hasOtherIncome,
      Boolean hasReviewedEmployment) {
    this.action = action;
    this.id = id;
    this.hasCapitalGains = hasCapitalGains;
    this.hasDividendsAndInterest = hasDividendsAndInterest;
    this.hasSocialSecurity = hasSocialSecurity;
    this.hasPension = hasPension;
    this.hasRetirementAccountDistributions = hasRetirementAccountDistributions;
    this.hasDisability = hasDisability;
    this.hasAlimony = hasAlimony;
    this.hasSeparateMaintenance = hasSeparateMaintenance;
    this.hasChildSupport = hasChildSupport;
    this.hasOtherIncome = hasOtherIncome;
    this.hasReviewedEmployment = hasReviewedEmployment;
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<Boolean> getHasCapitalGains() {
    return Option.of(hasCapitalGains);
  }

  public Option<Boolean> getHasDividendsAndInterest() {
    return Option.of(hasDividendsAndInterest);
  }

  public Option<Boolean> getHasSocialSecurity() {
    return Option.of(hasSocialSecurity);
  }

  public Option<Boolean> getHasPension() {
    return Option.of(hasPension);
  }

  public Option<Boolean> getHasRetirementAccountDistributions() {
    return Option.of(hasRetirementAccountDistributions);
  }

  public Option<Boolean> getHasDisability() {
    return Option.of(hasDisability);
  }

  public Option<Boolean> getHasAlimony() {
    return Option.of(hasAlimony);
  }

  public Option<Boolean> getHasSeparateMaintenance() {
    return Option.of(hasSeparateMaintenance);
  }

  public Option<Boolean> getHasChildSupport() {
    return Option.of(hasChildSupport);
  }

  public Option<Boolean> getHasOtherIncome() {
    return Option.of(hasOtherIncome);
  }

  public Option<Boolean> getHasReviewedEmployment() {
    return Option.of(hasReviewedEmployment);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.action, this.id, this.hasCapitalGains, this.hasDividendsAndInterest, this.hasSocialSecurity, this.hasPension, this.hasRetirementAccountDistributions, this.hasDisability, this.hasAlimony, this.hasSeparateMaintenance, this.hasChildSupport, this.hasOtherIncome, this.hasReviewedEmployment);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestIncomeWorkflowData that = (TestIncomeWorkflowData) o;
    return super.equals(that) &&
        Objects.equals(action, that.action) &&
        Objects.equals(id, that.id) &&
        Objects.equals(hasCapitalGains, that.hasCapitalGains) &&
        Objects.equals(hasDividendsAndInterest, that.hasDividendsAndInterest) &&
        Objects.equals(hasSocialSecurity, that.hasSocialSecurity) &&
        Objects.equals(hasPension, that.hasPension) &&
        Objects.equals(hasRetirementAccountDistributions, that.hasRetirementAccountDistributions) &&
        Objects.equals(hasDisability, that.hasDisability) &&
        Objects.equals(hasAlimony, that.hasAlimony) &&
        Objects.equals(hasSeparateMaintenance, that.hasSeparateMaintenance) &&
        Objects.equals(hasChildSupport, that.hasChildSupport) &&
        Objects.equals(hasOtherIncome, that.hasOtherIncome) &&
        Objects.equals(hasReviewedEmployment, that.hasReviewedEmployment);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestIncomeWorkflowData.class);
    if (isExactClass) {
      sb.append("TestIncomeWorkflowData {\n");
    }
    sb.append(super.toString());
    sb.append("  action: ").append(action).append("\n");
    sb.append("  id: ").append(id).append("\n");
    sb.append("  hasCapitalGains: ").append(hasCapitalGains).append("\n");
    sb.append("  hasDividendsAndInterest: ").append(hasDividendsAndInterest).append("\n");
    sb.append("  hasSocialSecurity: ").append(hasSocialSecurity).append("\n");
    sb.append("  hasPension: ").append(hasPension).append("\n");
    sb.append("  hasRetirementAccountDistributions: ").append(hasRetirementAccountDistributions).append("\n");
    sb.append("  hasDisability: ").append(hasDisability).append("\n");
    sb.append("  hasAlimony: ").append(hasAlimony).append("\n");
    sb.append("  hasSeparateMaintenance: ").append(hasSeparateMaintenance).append("\n");
    sb.append("  hasChildSupport: ").append(hasChildSupport).append("\n");
    sb.append("  hasOtherIncome: ").append(hasOtherIncome).append("\n");
    sb.append("  hasReviewedEmployment: ").append(hasReviewedEmployment).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withAction(getAction().getOrNull())
      .withId(getId().getOrNull())
      .withHasCapitalGains(getHasCapitalGains().getOrNull())
      .withHasDividendsAndInterest(getHasDividendsAndInterest().getOrNull())
      .withHasSocialSecurity(getHasSocialSecurity().getOrNull())
      .withHasPension(getHasPension().getOrNull())
      .withHasRetirementAccountDistributions(getHasRetirementAccountDistributions().getOrNull())
      .withHasDisability(getHasDisability().getOrNull())
      .withHasAlimony(getHasAlimony().getOrNull())
      .withHasSeparateMaintenance(getHasSeparateMaintenance().getOrNull())
      .withHasChildSupport(getHasChildSupport().getOrNull())
      .withHasOtherIncome(getHasOtherIncome().getOrNull())
      .withHasReviewedEmployment(getHasReviewedEmployment().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private String id = null;

    @Nullable
    private Boolean hasCapitalGains = null;

    @Nullable
    private Boolean hasDividendsAndInterest = null;

    @Nullable
    private Boolean hasSocialSecurity = null;

    @Nullable
    private Boolean hasPension = null;

    @Nullable
    private Boolean hasRetirementAccountDistributions = null;

    @Nullable
    private Boolean hasDisability = null;

    @Nullable
    private Boolean hasAlimony = null;

    @Nullable
    private Boolean hasSeparateMaintenance = null;

    @Nullable
    private Boolean hasChildSupport = null;

    @Nullable
    private Boolean hasOtherIncome = null;

    @Nullable
    private Boolean hasReviewedEmployment = null;

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withHasCapitalGains(@Nullable Boolean hasCapitalGains) {
      this.hasCapitalGains = hasCapitalGains;
      return this;
    }

    public Builder withHasDividendsAndInterest(@Nullable Boolean hasDividendsAndInterest) {
      this.hasDividendsAndInterest = hasDividendsAndInterest;
      return this;
    }

    public Builder withHasSocialSecurity(@Nullable Boolean hasSocialSecurity) {
      this.hasSocialSecurity = hasSocialSecurity;
      return this;
    }

    public Builder withHasPension(@Nullable Boolean hasPension) {
      this.hasPension = hasPension;
      return this;
    }

    public Builder withHasRetirementAccountDistributions(
        @Nullable Boolean hasRetirementAccountDistributions) {
      this.hasRetirementAccountDistributions = hasRetirementAccountDistributions;
      return this;
    }

    public Builder withHasDisability(@Nullable Boolean hasDisability) {
      this.hasDisability = hasDisability;
      return this;
    }

    public Builder withHasAlimony(@Nullable Boolean hasAlimony) {
      this.hasAlimony = hasAlimony;
      return this;
    }

    public Builder withHasSeparateMaintenance(@Nullable Boolean hasSeparateMaintenance) {
      this.hasSeparateMaintenance = hasSeparateMaintenance;
      return this;
    }

    public Builder withHasChildSupport(@Nullable Boolean hasChildSupport) {
      this.hasChildSupport = hasChildSupport;
      return this;
    }

    public Builder withHasOtherIncome(@Nullable Boolean hasOtherIncome) {
      this.hasOtherIncome = hasOtherIncome;
      return this;
    }

    public Builder withHasReviewedEmployment(@Nullable Boolean hasReviewedEmployment) {
      this.hasReviewedEmployment = hasReviewedEmployment;
      return this;
    }

    public TestIncomeWorkflowData build() {
      TestIncomeWorkflowData obj1 = new TestIncomeWorkflowData(action, id, hasCapitalGains, hasDividendsAndInterest, hasSocialSecurity, hasPension, hasRetirementAccountDistributions, hasDisability, hasAlimony, hasSeparateMaintenance, hasChildSupport, hasOtherIncome, hasReviewedEmployment);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestIncomeWorkflowData buildForTesting() {
      TestIncomeWorkflowData obj1 = new TestIncomeWorkflowData(action, id, hasCapitalGains, hasDividendsAndInterest, hasSocialSecurity, hasPension, hasRetirementAccountDistributions, hasDisability, hasAlimony, hasSeparateMaintenance, hasChildSupport, hasOtherIncome, hasReviewedEmployment);
      return obj1;
    }
  }
}
