package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestOccupancyBasis {
  OWN,

  RENT,

  RENT_FREE;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case OWN:
        return visitor.caseOwn();
      case RENT:
        return visitor.caseRent();
      case RENT_FREE:
        return visitor.caseRentFree();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseOwn();

    T caseRent();

    T caseRentFree();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseOwn() {
      return defaultValue.get();
    }

    @Override
    public T caseRent() {
      return defaultValue.get();
    }

    @Override
    public T caseRentFree() {
      return defaultValue.get();
    }
  }
}
