package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity
public class TestAddressData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private String line1;

  @Value(
      optional = true,
      nullable = true
  )
  private String line2;

  @Value(
      optional = true,
      nullable = true
  )
  private String city;

  @Value(
      optional = true,
      nullable = true
  )
  private String state;

  @Value(
      optional = true,
      nullable = true
  )
  private String county;

  @Value(
      optional = true,
      nullable = true
  )
  private String postalCode;

  @Value(
      optional = true,
      nullable = true
  )
  private String country;

  @Value(
      optional = true,
      nullable = true
  )
  private TestOccupancyBasis occupancyBasis;

  public TestAddressData() {
    // JSON
  }

  public TestAddressData(TestEntityAction action, String id, String line1, String line2,
      String city, String state, String county, String postalCode, String country,
      TestOccupancyBasis occupancyBasis) {
    this.action = action;
    this.id = id;
    this.line1 = line1;
    this.line2 = line2;
    this.city = city;
    this.state = state;
    this.county = county;
    this.postalCode = postalCode;
    this.country = country;
    this.occupancyBasis = occupancyBasis;
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<String> getLine1() {
    return Option.of(line1);
  }

  public Option<String> getLine2() {
    return Option.of(line2);
  }

  public Option<String> getCity() {
    return Option.of(city);
  }

  public Option<String> getState() {
    return Option.of(state);
  }

  public Option<String> getCounty() {
    return Option.of(county);
  }

  public Option<String> getPostalCode() {
    return Option.of(postalCode);
  }

  public Option<String> getCountry() {
    return Option.of(country);
  }

  public Option<TestOccupancyBasis> getOccupancyBasis() {
    return Option.of(occupancyBasis);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.action, this.id, this.line1, this.line2, this.city, this.state, this.county, this.postalCode, this.country, this.occupancyBasis);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestAddressData that = (TestAddressData) o;
    return super.equals(that) &&
        Objects.equals(action, that.action) &&
        Objects.equals(id, that.id) &&
        Objects.equals(line1, that.line1) &&
        Objects.equals(line2, that.line2) &&
        Objects.equals(city, that.city) &&
        Objects.equals(state, that.state) &&
        Objects.equals(county, that.county) &&
        Objects.equals(postalCode, that.postalCode) &&
        Objects.equals(country, that.country) &&
        Objects.equals(occupancyBasis, that.occupancyBasis);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestAddressData.class);
    if (isExactClass) {
      sb.append("TestAddressData {\n");
    }
    sb.append(super.toString());
    sb.append("  action: ").append(action).append("\n");
    sb.append("  id: ").append(id).append("\n");
    sb.append("  line1: ").append(line1).append("\n");
    sb.append("  line2: ").append(line2).append("\n");
    sb.append("  city: ").append(city).append("\n");
    sb.append("  state: ").append(state).append("\n");
    sb.append("  county: ").append(county).append("\n");
    sb.append("  postalCode: ").append(postalCode).append("\n");
    sb.append("  country: ").append(country).append("\n");
    sb.append("  occupancyBasis: ").append(occupancyBasis).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withAction(getAction().getOrNull())
      .withId(getId().getOrNull())
      .withLine1(getLine1().getOrNull())
      .withLine2(getLine2().getOrNull())
      .withCity(getCity().getOrNull())
      .withState(getState().getOrNull())
      .withCounty(getCounty().getOrNull())
      .withPostalCode(getPostalCode().getOrNull())
      .withCountry(getCountry().getOrNull())
      .withOccupancyBasis(getOccupancyBasis().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private String id = null;

    @Nullable
    private String line1 = null;

    @Nullable
    private String line2 = null;

    @Nullable
    private String city = null;

    @Nullable
    private String state = null;

    @Nullable
    private String county = null;

    @Nullable
    private String postalCode = null;

    @Nullable
    private String country = null;

    @Nullable
    private TestOccupancyBasis occupancyBasis = null;

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withLine1(@Nullable String line1) {
      this.line1 = line1;
      return this;
    }

    public Builder withLine2(@Nullable String line2) {
      this.line2 = line2;
      return this;
    }

    public Builder withCity(@Nullable String city) {
      this.city = city;
      return this;
    }

    public Builder withState(@Nullable String state) {
      this.state = state;
      return this;
    }

    public Builder withCounty(@Nullable String county) {
      this.county = county;
      return this;
    }

    public Builder withPostalCode(@Nullable String postalCode) {
      this.postalCode = postalCode;
      return this;
    }

    public Builder withCountry(@Nullable String country) {
      this.country = country;
      return this;
    }

    public Builder withOccupancyBasis(@Nullable TestOccupancyBasis occupancyBasis) {
      this.occupancyBasis = occupancyBasis;
      return this;
    }

    public TestAddressData build() {
      TestAddressData obj1 = new TestAddressData(action, id, line1, line2, city, state, county, postalCode, country, occupancyBasis);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestAddressData buildForTesting() {
      TestAddressData obj1 = new TestAddressData(action, id, line1, line2, city, state, county, postalCode, country, occupancyBasis);
      return obj1;
    }
  }
}
