package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestBorrowerType {
  PRIMARY,

  CO_BORROWER;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case PRIMARY:
        return visitor.casePrimary();
      case CO_BORROWER:
        return visitor.caseCoBorrower();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T casePrimary();

    T caseCoBorrower();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T casePrimary() {
      return defaultValue.get();
    }

    @Override
    public T caseCoBorrower() {
      return defaultValue.get();
    }
  }
}
