package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.entities.Money;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity
public class TestLoanAmountData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      optional = true,
      nullable = true
  )
  private Money purchasePrice;

  @Value(
      optional = true,
      nullable = true
  )
  private Money downPaymentAmount;

  @Value(
      optional = true,
      nullable = true
  )
  private Money loanAmount;

  public TestLoanAmountData() {
    // JSON
  }

  public TestLoanAmountData(String id, TestEntityAction action, Money purchasePrice,
      Money downPaymentAmount, Money loanAmount) {
    this.id = id;
    this.action = action;
    this.purchasePrice = purchasePrice;
    this.downPaymentAmount = downPaymentAmount;
    this.loanAmount = loanAmount;
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<Money> getPurchasePrice() {
    return Option.of(purchasePrice);
  }

  public Option<Money> getDownPaymentAmount() {
    return Option.of(downPaymentAmount);
  }

  public Option<Money> getLoanAmount() {
    return Option.of(loanAmount);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.id, this.action, this.purchasePrice, this.downPaymentAmount, this.loanAmount);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestLoanAmountData that = (TestLoanAmountData) o;
    return super.equals(that) &&
        Objects.equals(id, that.id) &&
        Objects.equals(action, that.action) &&
        Objects.equals(purchasePrice, that.purchasePrice) &&
        Objects.equals(downPaymentAmount, that.downPaymentAmount) &&
        Objects.equals(loanAmount, that.loanAmount);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestLoanAmountData.class);
    if (isExactClass) {
      sb.append("TestLoanAmountData {\n");
    }
    sb.append(super.toString());
    sb.append("  id: ").append(id).append("\n");
    sb.append("  action: ").append(action).append("\n");
    sb.append("  purchasePrice: ").append(purchasePrice).append("\n");
    sb.append("  downPaymentAmount: ").append(downPaymentAmount).append("\n");
    sb.append("  loanAmount: ").append(loanAmount).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withId(getId().getOrNull())
      .withAction(getAction().getOrNull())
      .withPurchasePrice(getPurchasePrice().getOrNull())
      .withDownPaymentAmount(getDownPaymentAmount().getOrNull())
      .withLoanAmount(getLoanAmount().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private String id = null;

    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private Money purchasePrice = null;

    @Nullable
    private Money downPaymentAmount = null;

    @Nullable
    private Money loanAmount = null;

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withPurchasePrice(@Nullable Money purchasePrice) {
      this.purchasePrice = purchasePrice;
      return this;
    }

    public Builder withDownPaymentAmount(@Nullable Money downPaymentAmount) {
      this.downPaymentAmount = downPaymentAmount;
      return this;
    }

    public Builder withLoanAmount(@Nullable Money loanAmount) {
      this.loanAmount = loanAmount;
      return this;
    }

    public TestLoanAmountData build() {
      TestLoanAmountData obj1 = new TestLoanAmountData(id, action, purchasePrice, downPaymentAmount, loanAmount);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestLoanAmountData buildForTesting() {
      TestLoanAmountData obj1 = new TestLoanAmountData(id, action, purchasePrice, downPaymentAmount, loanAmount);
      return obj1;
    }
  }
}
