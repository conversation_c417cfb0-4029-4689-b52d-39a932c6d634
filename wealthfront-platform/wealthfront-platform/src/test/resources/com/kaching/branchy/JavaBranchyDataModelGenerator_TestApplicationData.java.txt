package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity
public class TestApplicationData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private TestMortgageType mortgageType;

  @Value(
      optional = true,
      nullable = true
  )
  private List<TestBorrowerData> borrowers;

  @Value(
      optional = true,
      nullable = true
  )
  private TestLoanAmountData loanAmount;

  @Value(
      optional = true,
      nullable = true
  )
  private List<TestCreditPullData> creditPulls;

  public TestApplicationData() {
    // JSON
  }

  public TestApplicationData(String id, TestMortgageType mortgageType,
      List<TestBorrowerData> borrowers, TestLoanAmountData loanAmount,
      List<TestCreditPullData> creditPulls) {
    this.id = id;
    this.mortgageType = mortgageType;
    this.borrowers = borrowers;
    this.loanAmount = loanAmount;
    this.creditPulls = creditPulls;
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<TestMortgageType> getMortgageType() {
    return Option.of(mortgageType);
  }

  public Option<List<TestBorrowerData>> getBorrowers() {
    return Option.of(borrowers);
  }

  public Option<TestLoanAmountData> getLoanAmount() {
    return Option.of(loanAmount);
  }

  public Option<List<TestCreditPullData>> getCreditPulls() {
    return Option.of(creditPulls);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.id, this.mortgageType, this.borrowers, this.loanAmount, this.creditPulls);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestApplicationData that = (TestApplicationData) o;
    return super.equals(that) &&
        Objects.equals(id, that.id) &&
        Objects.equals(mortgageType, that.mortgageType) &&
        Objects.equals(borrowers, that.borrowers) &&
        Objects.equals(loanAmount, that.loanAmount) &&
        Objects.equals(creditPulls, that.creditPulls);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestApplicationData.class);
    if (isExactClass) {
      sb.append("TestApplicationData {\n");
    }
    sb.append(super.toString());
    sb.append("  id: ").append(id).append("\n");
    sb.append("  mortgageType: ").append(mortgageType).append("\n");
    sb.append("  borrowers: ").append(borrowers == null ? "null" : borrowers.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  loanAmount: ").append(loanAmount == null ? "null" : loanAmount.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  creditPulls: ").append(creditPulls == null ? "null" : creditPulls.toString().replaceAll("\n", "\n  ")).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withId(getId().getOrNull())
      .withMortgageType(getMortgageType().getOrNull())
      .withBorrowers(getBorrowers().getOrNull())
      .withLoanAmount(getLoanAmount().getOrNull())
      .withCreditPulls(getCreditPulls().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private String id = null;

    @Nullable
    private TestMortgageType mortgageType = null;

    @Nullable
    private List<TestBorrowerData> borrowers = new ArrayList<>();

    @Nullable
    private TestLoanAmountData loanAmount = null;

    @Nullable
    private List<TestCreditPullData> creditPulls = new ArrayList<>();

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withMortgageType(@Nullable TestMortgageType mortgageType) {
      this.mortgageType = mortgageType;
      return this;
    }

    public Builder withBorrowers(@Nullable List<TestBorrowerData> borrowers) {
      this.borrowers = borrowers;
      return this;
    }

    public Builder withLoanAmount(@Nullable TestLoanAmountData loanAmount) {
      this.loanAmount = loanAmount;
      return this;
    }

    public Builder withCreditPulls(@Nullable List<TestCreditPullData> creditPulls) {
      this.creditPulls = creditPulls;
      return this;
    }

    public TestApplicationData build() {
      TestApplicationData obj1 = new TestApplicationData(id, mortgageType, borrowers, loanAmount, creditPulls);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestApplicationData buildForTesting() {
      TestApplicationData obj1 = new TestApplicationData(id, mortgageType, borrowers, loanAmount, creditPulls);
      return obj1;
    }
  }
}
