package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditPullActionType {
  PULL_NEW,

  UPGRADE,

  RETRIEVE;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case PULL_NEW:
        return visitor.casePullNew();
      case UPGRADE:
        return visitor.caseUpgrade();
      case RETRIEVE:
        return visitor.caseRetrieve();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T casePullNew();

    T caseUpgrade();

    T caseRetrieve();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T casePullNew() {
      return defaultValue.get();
    }

    @Override
    public T caseUpgrade() {
      return defaultValue.get();
    }

    @Override
    public T caseRetrieve() {
      return defaultValue.get();
    }
  }
}
