package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestMaritalStatus {
  MARRIED,

  SEPARATED,

  UNMARRIED;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case MARRIED:
        return visitor.caseMarried();
      case SEPARATED:
        return visitor.caseSeparated();
      case UNMARRIED:
        return visitor.caseUnmarried();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseMarried();

    T caseSeparated();

    T caseUnmarried();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseMarried() {
      return defaultValue.get();
    }

    @Override
    public T caseSeparated() {
      return defaultValue.get();
    }

    @Override
    public T caseUnmarried() {
      return defaultValue.get();
    }
  }
}
