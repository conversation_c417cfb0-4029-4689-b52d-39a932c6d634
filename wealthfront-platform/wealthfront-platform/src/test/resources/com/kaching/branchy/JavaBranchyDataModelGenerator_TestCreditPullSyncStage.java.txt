package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditPullSyncStage {
  INITIATE_CREDIT_PULL,

  POLL_CREDIT_PULL_STATUS,

  RETRIEVE_LOAN,

  COMPLETE;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case INITIATE_CREDIT_PULL:
        return visitor.caseInitiateCreditPull();
      case POLL_CREDIT_PULL_STATUS:
        return visitor.casePollCreditPullStatus();
      case RETRIEVE_LOAN:
        return visitor.caseRetrieveLoan();
      case COMPLETE:
        return visitor.caseComplete();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseInitiateCreditPull();

    T casePollCreditPullStatus();

    T caseRetrieveLoan();

    T caseComplete();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseInitiateCreditPull() {
      return defaultValue.get();
    }

    @Override
    public T casePollCreditPullStatus() {
      return defaultValue.get();
    }

    @Override
    public T caseRetrieveLoan() {
      return defaultValue.get();
    }

    @Override
    public T caseComplete() {
      return defaultValue.get();
    }
  }
}
